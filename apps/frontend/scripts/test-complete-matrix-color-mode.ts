#!/usr/bin/env tsx

/**
 * 测试完整矩阵数据集的颜色模式渲染
 * 验证purple和pink颜色是否被正确过滤
 */

import { getCachedCompleteData, getMatrixDataByColor } from '../core/data/GroupAData';
import { matrixCore } from '../core/matrix/MatrixCore';
import type { MatrixData, MatrixConfig } from '../core/matrix/MatrixTypes';
import { coordinateKey } from '../core/matrix/MatrixTypes';

console.log('🧪 测试完整矩阵数据集的颜色模式渲染...\n');

// 测试1: 获取完整数据集
console.log('📊 测试1: 获取完整数据集');
const completeData = getCachedCompleteData();
console.log(`✅ 完整数据集总点数: ${completeData.metadata.totalPoints}`);
console.log(`✅ 包含的组数: ${Object.keys(completeData.metadata.groupCounts).filter(group => completeData.metadata.groupCounts[group] > 0).length}`);

// 测试2: 检查purple和pink数据点
console.log('\n📊 测试2: 检查purple和pink数据点');
const purplePoints = getMatrixDataByColor(completeData, 'purple');
const pinkPoints = getMatrixDataByColor(completeData, 'pink');
console.log(`✅ Purple数据点数量: ${purplePoints.length}`);
console.log(`✅ Pink数据点数量: ${pinkPoints.length}`);

// 测试3: 测试颜色模式处理器
console.log('\n📊 测试3: 测试颜色模式处理器');

// 创建测试用的矩阵数据
const testMatrixData: MatrixData = {
  cells: new Map(),
  selectedCells: new Set(),
  hoveredCell: null,
  focusedCell: null,
};

// 添加一些测试单元格，包括purple和pink颜色的位置
const testCells = [
  { x: 16, y: 16, color: 'red' as const },
  { x: 16, y: 24, color: 'purple' as const }, // 这个应该被过滤
  { x: 20, y: 20, color: 'pink' as const },   // 这个应该被过滤
  { x: 12, y: 12, color: 'blue' as const },
];

testCells.forEach(cell => {
  const key = coordinateKey(cell.x, cell.y);
  testMatrixData.cells.set(key, {
    x: cell.x,
    y: cell.y,
    color: cell.color,
    isActive: true,
    isSelected: false,
    isHovered: false,
    level: 1,
    group: 'A',
    word: null,
  });
});

const testConfig: MatrixConfig = {
  mode: 'color',
};

// 处理数据
const processedData = matrixCore.processData(testMatrixData, testConfig);
console.log(`✅ 处理后的渲染数据数量: ${processedData.renderData.size}`);

// 测试4: 验证purple和pink是否被过滤
console.log('\n📊 测试4: 验证purple和pink是否被过滤');

let purpleFiltered = true;
let pinkFiltered = true;

processedData.renderData.forEach((renderData, key) => {
  if (renderData.content === 'purple') {
    purpleFiltered = false;
    console.log(`❌ 发现未过滤的purple数据: ${key}`);
  }
  if (renderData.content === 'pink') {
    pinkFiltered = false;
    console.log(`❌ 发现未过滤的pink数据: ${key}`);
  }
});

if (purpleFiltered) {
  console.log('✅ Purple颜色已正确过滤');
} else {
  console.log('❌ Purple颜色未被正确过滤');
}

if (pinkFiltered) {
  console.log('✅ Pink颜色已正确过滤');
} else {
  console.log('❌ Pink颜色未被正确过滤');
}

// 测试5: 验证其他颜色正常显示
console.log('\n📊 测试5: 验证其他颜色正常显示');

const allowedColors = ['black', 'red', 'cyan', 'yellow', 'orange', 'green', 'blue'];
let otherColorsWorking = true;

processedData.renderData.forEach((renderData, key) => {
  if (renderData.content && !allowedColors.includes(renderData.content as string) && renderData.content !== 'A') {
    otherColorsWorking = false;
    console.log(`❌ 发现不允许的颜色内容: ${renderData.content} at ${key}`);
  }
});

if (otherColorsWorking) {
  console.log('✅ 其他颜色正常显示');
} else {
  console.log('❌ 其他颜色显示异常');
}

// 测试6: 检查完整数据集中的实际数据点
console.log('\n📊 测试6: 检查完整数据集中的实际数据点');

// 找一些实际存在purple和pink数据的坐标
if (purplePoints.length > 0) {
  const purplePoint = purplePoints[0];
  console.log(`Purple数据点示例: (${purplePoint.x}, ${purplePoint.y}) 组${purplePoint.group} 级别${purplePoint.level}`);
  
  // 测试这个坐标的渲染
  const purpleKey = coordinateKey(purplePoint.x, purplePoint.y);
  testMatrixData.cells.set(purpleKey, {
    x: purplePoint.x,
    y: purplePoint.y,
    color: 'purple',
    isActive: true,
    isSelected: false,
    isHovered: false,
    level: purplePoint.level,
    group: purplePoint.group,
    word: null,
  });
  
  const reprocessedData = matrixCore.processData(testMatrixData, testConfig);
  const purpleRenderData = reprocessedData.renderData.get(purpleKey);
  
  if (purpleRenderData && purpleRenderData.content === '') {
    console.log('✅ Purple数据点被正确过滤（内容为空）');
  } else {
    console.log(`❌ Purple数据点未被正确过滤，内容: ${purpleRenderData?.content}`);
  }
}

if (pinkPoints.length > 0) {
  const pinkPoint = pinkPoints[0];
  console.log(`Pink数据点示例: (${pinkPoint.x}, ${pinkPoint.y}) 组${pinkPoint.group} 级别${pinkPoint.level}`);
}

console.log('\n🎉 测试完成！');

// 输出统计信息
console.log('\n📈 统计信息:');
console.log(`- 完整数据集包含 ${completeData.metadata.totalPoints} 个数据点`);
console.log(`- Purple数据点: ${purplePoints.length} 个`);
console.log(`- Pink数据点: ${pinkPoints.length} 个`);
console.log(`- 其他颜色数据点: ${completeData.metadata.totalPoints - purplePoints.length - pinkPoints.length} 个`);

const colorCounts = completeData.metadata.colorCounts;
Object.entries(colorCounts).forEach(([color, count]) => {
  if (count > 0) {
    console.log(`- ${color}: ${count} 个数据点`);
  }
});
